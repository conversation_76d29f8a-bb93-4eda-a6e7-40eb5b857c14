#!/usr/bin/env python3
"""
String manipulation example demonstrating replace() and upper() functions
"""

# Save the sentence as a single string
original_sentence = "The!quick!brown!fox!jumps!over!the!lazy!dog."

print("Original sentence:")
print(original_sentence)
print()

# Replace every "!" exclamation mark with a blank space using replace()
sentence_with_spaces = original_sentence.replace("!", " ")
print("After replacing '!' with spaces:")
print(sentence_with_spaces)
print()

# Convert to uppercase using upper()
uppercase_sentence = sentence_with_spaces.upper()
print("After converting to uppercase:")
print(uppercase_sentence)
print()

# Print the sentence in reverse
reversed_sentence = original_sentence[::-1]
print("Original sentence in reverse:")
print(reversed_sentence)

#!/usr/bin/env python3
"""
Interactive string manipulation program
"""

# Ask the user to enter a sentence using input() method
str_manip = input("Please enter a sentence: ")

print(f"\nYou entered: {str_manip}")
print()


# Calculate and display the length of str_manip
length = len(str_manip)
print(f"Length of the sentence: {length}")
print()

# Find the last letter in str_manip and replace every occurrence with '@'
last_letter = str_manip[-1]
replaced_string = str_manip.replace(last_letter, '@')
print(f"Last letter is: '{last_letter}'")
print(f"String with last letter replaced by '@': {replaced_string}")
print()

# Print the last 3 characters in str_manip backwards
last_three_backwards = str_manip[-3:][::-1]
print(f"Last 3 characters backwards: {last_three_backwards}")
print()

# Create a five-letter word from first three and last two characters
first_three = str_manip[:3]
last_two = str_manip[-2:]
five_letter_word = first_three + last_two
print(f"Five-letter word (first 3 + last 2 characters): {five_letter_word}")
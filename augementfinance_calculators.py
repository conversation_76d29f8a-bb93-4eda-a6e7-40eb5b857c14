import math

# Financial Calculators Program
# This program allows users to calculate investment returns or home loan repayments

print("Investment - to calculate the amount of interest you'll earn on your investment.")
print("Bond - to calculate the amount you'll have to pay on a home loan.")
print()

# Get user's choice of calculation with error handling loop
# Source: W3Schools Python While Loops - https://www.w3schools.com/python/python_while_loops.asp
while True:
    user_choice = input("Enter either 'investment' or 'bond' from the menu above to proceed: ").lower()
    if user_choice == "investment" or user_choice == "bond":
        break
    else:
        print("Error: Invalid selection. Please enter either 'investment' or 'bond'.")
        print()

# Proceed with appropriate calculation
if user_choice == "investment":
    print("\n--- Investment Calculator ---")
    
    # Get investment details from user
    principal = float(input("Enter the amount of money you are depositing: "))
    interest_rate = float(input("Enter the interest rate (as a percentage): "))
    years = int(input("Enter the number of years you plan on investing: "))
    # Get interest type with error handling loop
    # Source: W3Schools Python While Loops - https://www.w3schools.com/python/python_while_loops.asp
    while True:
        interest_type = input("Do you want 'simple' or 'compound' interest? ").lower()
        if interest_type == "simple" or interest_type == "compound":
            break
        else:
            print("Error: Please enter either 'simple' or 'compound' for interest type.")

    # Convert percentage to decimal
    r = interest_rate / 100

    # Calculate based on interest type
    if interest_type == "simple":
        # Simple interest formula: A = P(1 + r*t)
        total_amount = principal * (1 + r * years)
        print(f"\nWith simple interest, your investment will be worth: R{round(total_amount, 2)}")

    elif interest_type == "compound":
        # Compound interest formula: A = P(1 + r)^t
        total_amount = principal * math.pow((1 + r), years)
        print(f"\nWith compound interest, your investment will be worth: R{round(total_amount, 2)}")

elif user_choice == "bond":
    print("\n--- Bond Repayment Calculator ---")
    
    # Get bond details from user
    house_value = float(input("Enter the present value of the house: "))
    annual_interest_rate = float(input("Enter the interest rate (as a percentage): "))
    months = int(input("Enter the number of months to repay the bond: "))
    
    # Calculate monthly interest rate
    monthly_interest_rate = (annual_interest_rate / 100) / 12
    
    # Bond repayment formula: repayment = (i * P) / (1 - (1 + i)^(-n))
    monthly_repayment = (monthly_interest_rate * house_value) / (1 - (1 + monthly_interest_rate)**(-months))
    
    print(f"\nYour monthly bond repayment will be: R{round(monthly_repayment, 2)}")

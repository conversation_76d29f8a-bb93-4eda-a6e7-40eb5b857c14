# Ask the user to enter a sentence using input() method
str_manip = input("Please enter a sentence: ")
print(f"You entered: {str_manip}")

# Calculate and display the length of str_manip
length = len(str_manip)
print(f"Length of the sentence: {length}")

#find the last letter in str_manip and replace every occurrence with '@'
last_letter = str_manip[-1]
replaced_string = str_manip.replace(last_letter, '@')
print(f'The last letter is: {last_letter}')
print(f"The new string with the last letter replaced by '@': {replaced_string}")

#Print the last 3 letters in str_manip backwards
last_three_backwards = str_manip[-3:][::-1]
print(f"The last 3 letters backwards: {last_three_backwards}")


#create a five letter word from the first three and last two characters of str_manip
first_three = str_manip[:3]
last_two = str_manip[-2:]
five_letter_word = first_three + last_two
print(f"Five-letter word (first 3 + last 2 characters): {five_letter_word}")